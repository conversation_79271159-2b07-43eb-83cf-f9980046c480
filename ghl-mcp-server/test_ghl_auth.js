#!/usr/bin/env node

/**
 * Test script to debug GoHighLevel API authentication
 */

const axios = require('axios');
require('dotenv').config();

const API_KEY = process.env.GHL_API_KEY;
const LOCATION_ID = process.env.GHL_LOCATION_ID;

console.log('🧪 Testing GoHighLevel API Authentication');
console.log('=====================================');
console.log(`API Key: ${API_KEY ? API_KEY.substring(0, 20) + '...' : 'NOT SET'}`);
console.log(`Location ID: ${LOCATION_ID || 'NOT SET'}`);
console.log('');

if (!API_KEY || !LOCATION_ID) {
    console.log('❌ Missing required environment variables');
    console.log('Please set GHL_API_KEY and GHL_LOCATION_ID in your .env file');
    process.exit(1);
}

// Test configurations to try
const testConfigs = [
    {
        name: 'Current Config (services.leadconnectorhq.com)',
        baseURL: 'https://services.leadconnectorhq.com',
        headers: {
            'Authorization': `Bearer ${API_KEY}`,
            'Version': '2021-07-28',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
    },
    {
        name: 'Alternative Config (api.gohighlevel.com/v1)',
        baseURL: 'https://api.gohighlevel.com/v1',
        headers: {
            'Authorization': `Bearer ${API_KEY}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
    },
    {
        name: 'Alternative Headers (services.leadconnectorhq.com)',
        baseURL: 'https://services.leadconnectorhq.com',
        headers: {
            'Authorization': `Bearer ${API_KEY}`,
            'Version': '2021-04-15',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
    }
];

async function testConfig(config) {
    console.log(`\n🔍 Testing: ${config.name}`);
    console.log(`   Base URL: ${config.baseURL}`);
    console.log(`   Headers: ${JSON.stringify(config.headers, null, 6)}`);
    
    try {
        const response = await axios.get('/contacts/', {
            baseURL: config.baseURL,
            headers: config.headers,
            params: {
                locationId: LOCATION_ID,
                limit: 1
            },
            timeout: 10000
        });
        
        console.log(`   ✅ SUCCESS: Status ${response.status}`);
        console.log(`   📊 Response: ${JSON.stringify(response.data, null, 6)}`);
        return true;
    } catch (error) {
        console.log(`   ❌ FAILED: ${error.response?.status || 'Network Error'}`);
        console.log(`   📝 Error: ${error.response?.data?.message || error.message}`);
        if (error.response?.data) {
            console.log(`   📋 Details: ${JSON.stringify(error.response.data, null, 6)}`);
        }
        return false;
    }
}

async function main() {
    let successCount = 0;
    
    for (const config of testConfigs) {
        const success = await testConfig(config);
        if (success) successCount++;
    }
    
    console.log('\n📊 Summary:');
    console.log('===========');
    console.log(`✅ Successful configs: ${successCount}/${testConfigs.length}`);
    
    if (successCount === 0) {
        console.log('\n🔧 Troubleshooting Steps:');
        console.log('1. Verify your GHL_API_KEY is current and valid');
        console.log('2. Check that your GoHighLevel account is active');
        console.log('3. Ensure you\'re using the Location API Key (not Private Integrations)');
        console.log('4. Try generating a new Location API Key from: Settings → Company → API Key');
        console.log('5. Verify your Location ID is correct');
    }
}

main().catch(console.error);
